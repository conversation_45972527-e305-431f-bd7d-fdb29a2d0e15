import React, { useState } from "react";
import { CheckSquare, Square, ChevronDown, HelpCircle } from "react-feather";
import { <PERSON><PERSON>, ModalContent, ModalBody } from "@heroui/react";
import { Button } from "../ui/button";
import { BasketItem } from "../../types/basket";
import { useEmbeddedCheckout } from "../../hooks/useEmbeddedCheckout";
import EmbeddedCheckout from "../EmbeddedCheckout";

interface ConfirmPaymentProps {
  selectedItem: BasketItem | null;
  onConfirm: () => void;
  currencySymbol?: string;
  profileDetails?: {
    email?: any;
    stripe_id?: any;
    id: string;
    avatar?: string;
    profile_name?: string;
    currency?: string;
  } | null;
  userProfileDetails?: {
    email?: any;
    profile_name?: any;
    id: string;
  } | null;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: React.ReactNode;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: "stripe",
    name: "Stripe",
    icon: <img src="/assets/stripe.svg" alt="" className="w-4 h-4 object-cover" />,
  },
];

const ConfirmPaymentEmbedded: React.FC<ConfirmPaymentProps> = ({
  selectedItem,
  onConfirm,
  currencySymbol = "$",
  profileDetails,
  userProfileDetails,
}) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("stripe");
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [orderPlacedSuccessfully, setOrderPlacedSuccessfully] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);

  const { clientSecret, loading, error, createCheckoutSession, reset } = useEmbeddedCheckout();

  const handlePaymentConfirm = async () => {
    if (!selectedItem || !userProfileDetails || !profileDetails) {
      console.error("Missing required data for payment");
      return;
    }

    try {
      const checkoutData = {
        userId: userProfileDetails.id,
        userEmail: userProfileDetails.email,
        sellerId: profileDetails.id,
        sellerEmail: profileDetails.email,
        sellerStripeAccountId: profileDetails.stripe_id,
        orderId: selectedItem.id,
        amount: Math.round(selectedItem.subtotal * 100), // Convert to cents
        currency: profileDetails.currency?.toLowerCase() || 'usd',
        productName: selectedItem.title,
        productDescription: selectedItem.description,
        isEscrow: true,
      };

      await createCheckoutSession(checkoutData);
      setShowCheckout(true);
      setOrderPlacedSuccessfully(true);
    } catch (error) {
      console.error("❌ Error in handlePaymentConfirm:", error);
    }
  };

  const handleBackToPaymentMethods = () => {
    setShowCheckout(false);
    reset();
  };

  const handleCloseModal = () => {
    setIsPaymentModalOpen(false);
    setShowCheckout(false);
    reset();
    onConfirm();
  };

  if (!selectedItem) {
    return null;
  }

  return (
    <div className="flex flex-col mr-4">
      <Button
        onClick={() => setIsPaymentModalOpen(true)}
        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
      >
        Confirm Payment
      </Button>

      <Modal
        isOpen={isPaymentModalOpen}
        onClose={handleCloseModal}
        size="2xl"
        classNames={{
          base: "max-h-[90vh]",
          body: "p-0",
        }}
      >
        <ModalContent>
          <ModalBody>
            <div className="p-6">
              {!showCheckout ? (
                // Payment method selection
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">Confirm Payment</h2>
                    <button
                      onClick={handleCloseModal}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      ✕
                    </button>
                  </div>

                  {/* Order Summary */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 className="font-medium mb-2">Order Summary</h3>
                    <div className="flex justify-between items-center">
                      <span>{selectedItem.title}</span>
                      <span className="font-medium">
                        {currencySymbol}{selectedItem.subtotal.toFixed(2)}
                      </span>
                    </div>
                    {selectedItem.description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {selectedItem.description}
                      </p>
                    )}
                  </div>

                  {/* Payment Methods */}
                  <div className="mb-6">
                    <h3 className="font-medium mb-3">Payment Method</h3>
                    <div className="space-y-2">
                      {paymentMethods.map((method) => (
                        <div
                          key={method.id}
                          className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                            selectedPaymentMethod === method.id
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => setSelectedPaymentMethod(method.id)}
                        >
                          <div className="mr-3">
                            {selectedPaymentMethod === method.id ? (
                              <CheckSquare className="w-5 h-5 text-blue-600" />
                            ) : (
                              <Square className="w-5 h-5 text-gray-400" />
                            )}
                          </div>
                          <div className="flex items-center">
                            {method.icon}
                            <span className="ml-2 font-medium">{method.name}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Error Display */}
                  {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                      {error}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <Button
                      onClick={handleCloseModal}
                      variant="outline"
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handlePaymentConfirm}
                      disabled={loading}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {loading ? "Creating Session..." : "Proceed to Payment"}
                    </Button>
                  </div>
                </div>
              ) : (
                // Embedded Checkout
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                      <button
                        onClick={handleBackToPaymentMethods}
                        className="mr-3 text-gray-400 hover:text-gray-600"
                      >
                        ←
                      </button>
                      <h2 className="text-xl font-semibold">Complete Payment</h2>
                    </div>
                    <button
                      onClick={handleCloseModal}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      ✕
                    </button>
                  </div>

                  {/* Order Summary (smaller version) */}
                  <div className="bg-gray-50 rounded-lg p-3 mb-4">
                    <div className="flex justify-between items-center text-sm">
                      <span>{selectedItem.title}</span>
                      <span className="font-medium">
                        {currencySymbol}{selectedItem.price.toFixed(2)}
                      </span>
                    </div>
                  </div>

                  {/* Embedded Checkout Form */}
                  {clientSecret && (
                    <div className="max-h-[60vh] overflow-y-auto">
                      <EmbeddedCheckout clientSecret={clientSecret} />
                    </div>
                  )}
                </div>
              )}
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ConfirmPaymentEmbedded;
